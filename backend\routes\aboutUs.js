import express from 'express';
import AboutUs from '../models/AboutUs.js';

const router = express.Router();

// GET /api/about-us - Get About Us content for frontend (public)
router.get('/', async (req, res) => {
  try {
    const { lang = 'fr' } = req.query;
    
    let aboutUs = await AboutUs.findOne();
    
    if (!aboutUs) {
      return res.status(404).json({ message: 'About Us content not found' });
    }

    // Transform data for frontend consumption based on language
    const transformedData = {
      heroSection: {
        title: aboutUs.heroSection.title[lang] || aboutUs.heroSection.title.fr,
        highlightedText: aboutUs.heroSection.highlightedText[lang] || aboutUs.heroSection.highlightedText.fr,
        subtitle: aboutUs.heroSection.subtitle[lang] || aboutUs.heroSection.subtitle.fr,
        buttonText: aboutUs.heroSection.buttonText[lang] || aboutUs.heroSection.buttonText.fr,
        backgroundImage: aboutUs.heroSection.backgroundImage
      },
      coreValues: {
        vision: {
          title: aboutUs.coreValues.vision.title[lang] || aboutUs.coreValues.vision.title.fr,
          description: aboutUs.coreValues.vision.description[lang] || aboutUs.coreValues.vision.description.fr,
          points: aboutUs.coreValues.vision.points[lang] || aboutUs.coreValues.vision.points.fr,
          image: aboutUs.coreValues.vision.image
        },
        mission: {
          title: aboutUs.coreValues.mission.title[lang] || aboutUs.coreValues.mission.title.fr,
          description: aboutUs.coreValues.mission.description[lang] || aboutUs.coreValues.mission.description.fr,
          points: aboutUs.coreValues.mission.points[lang] || aboutUs.coreValues.mission.points.fr,
          image: aboutUs.coreValues.mission.image
        },
        story: {
          title: aboutUs.coreValues.story.title[lang] || aboutUs.coreValues.story.title.fr,
          description: aboutUs.coreValues.story.description[lang] || aboutUs.coreValues.story.description.fr,
          points: aboutUs.coreValues.story.points[lang] || aboutUs.coreValues.story.points.fr,
          image: aboutUs.coreValues.story.image
        }
      },
      timeline: aboutUs.timeline.map(item => ({
        year: item.year,
        title: item.title[lang] || item.title.fr,
        description: item.description[lang] || item.description.fr,
        milestones: item.milestones[lang] || item.milestones.fr
      })),
      ourMembers: {
        title: aboutUs.ourMembers.title[lang] || aboutUs.ourMembers.title.fr,
        subtitle: aboutUs.ourMembers.subtitle[lang] || aboutUs.ourMembers.subtitle.fr,
        members: aboutUs.ourMembers.members.map(member => ({
          name: member.name[lang] || member.name.fr,
          position: member.position[lang] || member.position.fr,
          description: member.description[lang] || member.description.fr,
          image: member.image,
          order: member.order
        }))
      }
    };

    res.json(transformedData);
  } catch (error) {
    console.error('Error fetching About Us content:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// GET /api/about-us/admin - Get raw About Us data for admin dashboard
router.get('/admin', async (req, res) => {
  try {
    let aboutUs = await AboutUs.findOne();
    
    if (!aboutUs) {
      return res.status(404).json({ message: 'About Us content not found' });
    }

    res.json(aboutUs);
  } catch (error) {
    console.error('Error fetching About Us admin data:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// PUT /api/about-us - Create or update About Us content (admin only)
router.put('/', async (req, res) => {
  try {
    const aboutUsData = req.body;

    // Validate required structure
    if (!aboutUsData.heroSection || !aboutUsData.coreValues || !aboutUsData.ourMembers) {
      return res.status(400).json({ message: 'Invalid About Us data structure' });
    }

    let aboutUs = await AboutUs.findOne();

    if (aboutUs) {
      // Update existing document
      Object.assign(aboutUs, aboutUsData);
      await aboutUs.save();
    } else {
      // Create new document
      aboutUs = new AboutUs(aboutUsData);
      await aboutUs.save();
    }

    res.json(aboutUs);
  } catch (error) {
    console.error('Error saving About Us content:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// DELETE /api/about-us - Delete About Us content (admin only)
router.delete('/', async (req, res) => {
  try {
    const aboutUs = await AboutUs.findOneAndDelete();
    
    if (!aboutUs) {
      return res.status(404).json({ message: 'About Us content not found' });
    }

    res.json({ message: 'About Us content deleted successfully' });
  } catch (error) {
    console.error('Error deleting About Us content:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

export default router;
