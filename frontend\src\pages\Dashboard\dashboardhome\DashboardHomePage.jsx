import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Package, CheckCircle, DollarSign, RefreshCw } from 'lucide-react';

const DashboardHomePage = () => {
    const [stats, setStats] = useState({
        totalRevenue: 0,
        pendingOrders: 0,
        totalProducts: 0,
    });
    const [loading, setLoading] = useState(true);

    const fetchStats = async () => {
      setLoading(true);
      try {
        const response = await axios.get('http://localhost:5000/api/dashboard/stats');
        setStats(response.data);
      } catch (error) {
        console.error("Failed to fetch dashboard stats", error);
      } finally {
        setLoading(false);
      }
    };

    useEffect(() => {
      fetchStats();
    }, []);

    return (
        <div>
            <div className="flex justify-between items-center mb-4">
                <h1 className="text-3xl font-bold text-gray-800">Welcome to your Dashboard</h1>
                <button onClick={fetchStats} disabled={loading} className="p-2 text-gray-500 hover:text-indigo-600 disabled:text-gray-300 disabled:cursor-wait" title="Refresh Stats">
                    <RefreshCw className={loading ? 'animate-spin' : ''} />
                </button>
            </div>
            <p className="text-gray-600 mb-8">
                Here's a quick overview of your store's performance.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white p-6 rounded-lg shadow flex items-center gap-5">
                    <div className="p-3 bg-green-100 rounded-full"><DollarSign className="text-green-600" size={28}/></div>
                    <div>
                        <h2 className="text-sm font-medium text-gray-500">Total Confirmed Revenue</h2>
                        <p className="text-3xl font-bold text-gray-800">${stats.totalRevenue.toFixed(2)}</p>
                    </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow flex items-center gap-5">
                    <div className="p-3 bg-yellow-100 rounded-full"><Package className="text-yellow-600" size={28}/></div>
                    <div>
                        <h2 className="text-sm font-medium text-gray-500">Pending Orders</h2>
                        <p className="text-3xl font-bold text-gray-800">{stats.pendingOrders}</p>
                    </div>
                </div>
                <div className="bg-white p-6 rounded-lg shadow flex items-center gap-5">
                    <div className="p-3 bg-indigo-100 rounded-full"><CheckCircle className="text-indigo-600" size={28}/></div>
                    <div>
                        <h2 className="text-sm font-medium text-gray-500">Total Products</h2>
                        <p className="text-3xl font-bold text-gray-800">{stats.totalProducts}</p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DashboardHomePage;
