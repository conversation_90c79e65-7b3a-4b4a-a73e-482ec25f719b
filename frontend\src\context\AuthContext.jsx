import React, { createContext, useState, useEffect, useContext } from 'react';

// 1. Create the context
export const AuthContext = createContext();

// Custom hook to use the AuthContext
export const useAuth = () => {
  return useContext(AuthContext);
};

// 2. Create the provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true); // To handle initial auth state check

  // Effect to check for existing user info in localStorage on app start
  useEffect(() => {
    try {
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        const userInfo = JSON.parse(storedUserInfo);
        setUser(userInfo);
        setToken(userInfo.token);
      }
    } catch (error) {
      console.error("Failed to parse user info from localStorage", error);
      // If parsing fails, ensure state is clean
      setUser(null);
      setToken(null);
      localStorage.removeItem('userInfo');
    } finally {
      setLoading(false); // Stop loading once check is complete
    }
  }, []);

  // Effect to periodically refresh user data to catch role changes
  useEffect(() => {
    if (!token || !user) return;

    // Refresh user data every 5 minutes
    const interval = setInterval(() => {
      refreshUser();
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [token, user]);

  // Login function: stores user info in state and localStorage
  const login = (userData) => {
    console.log('AuthContext: User logged in with role:', userData.role);
    setUser(userData);
    setToken(userData.token);
    localStorage.setItem('userInfo', JSON.stringify(userData));
  };

  // Logout function: clears user info from state and localStorage
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('userInfo');
  };

  // Refresh user data function: fetches current user data from server
  const refreshUser = async () => {
    try {
      if (!token) return;

      // Import axios here to avoid circular dependency
      const axios = (await import('axios')).default;
      const response = await axios.get('/api/users/profile');

      const updatedUserData = {
        ...user,
        ...response.data.data,
        token: token // Keep the existing token
      };

      console.log('AuthContext: User data refreshed. Old role:', user?.role, 'New role:', updatedUserData.role);
      setUser(updatedUserData);
      localStorage.setItem('userInfo', JSON.stringify(updatedUserData));
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      // If refresh fails and it's an auth error, logout the user
      if (error.response?.status === 401) {
        logout();
      }
    }
  };

  // Role-based utility functions
  const hasRole = (role) => {
    return user?.role === role;
  };

  const hasAnyRole = (roles) => {
    return roles.includes(user?.role);
  };

  const canAccessDashboard = () => {
    return user?.role && user.role !== 'buyer';
  };

  const hasPermission = (permission) => {
    const rolePermissions = {
      buyer: [],
      checker: ['orders', 'overview'],
      creator: ['overview', 'products', 'categories', 'navbar'],
      controller: ['overview', 'footer', 'about-us', 'contact-us'],
      admin: ['overview', 'products', 'categories', 'navbar', 'orders', 'footer', 'about-us', 'contact-us', 'slider', 'users']
    };

    const userPermissions = rolePermissions[user?.role] || [];
    return userPermissions.includes(permission);
  };

  // The value provided to consuming components
  const value = {
    user,
    token,
    isLoggedIn: !!token, // Convenience boolean
    loading,
    login,
    logout,
    refreshUser,
    // Role utilities
    hasRole,
    hasAnyRole,
    canAccessDashboard,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};