import React, { createContext, useState, useEffect, useContext } from 'react';

// 1. Create the context
export const AuthContext = createContext();

// Custom hook to use the AuthContext
export const useAuth = () => {
  return useContext(AuthContext);
};

// 2. Create the provider component
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [loading, setLoading] = useState(true); // To handle initial auth state check

  // Effect to check for existing user info in localStorage on app start
  useEffect(() => {
    try {
      const storedUserInfo = localStorage.getItem('userInfo');
      if (storedUserInfo) {
        const userInfo = JSON.parse(storedUserInfo);
        setUser(userInfo);
        setToken(userInfo.token);
      }
    } catch (error) {
      console.error("Failed to parse user info from localStorage", error);
      // If parsing fails, ensure state is clean
      setUser(null);
      setToken(null);
      localStorage.removeItem('userInfo');
    } finally {
      setLoading(false); // Stop loading once check is complete
    }
  }, []);

  // Login function: stores user info in state and localStorage
  const login = (userData) => {
    setUser(userData);
    setToken(userData.token);
    localStorage.setItem('userInfo', JSON.stringify(userData));
  };

  // Logout function: clears user info from state and localStorage
  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('userInfo');
  };

  // Role-based utility functions
  const hasRole = (role) => {
    return user?.role === role;
  };

  const hasAnyRole = (roles) => {
    return roles.includes(user?.role);
  };

  const canAccessDashboard = () => {
    return user?.role && user.role !== 'buyer';
  };

  const hasPermission = (permission) => {
    const rolePermissions = {
      buyer: [],
      checker: ['orders', 'overview'],
      creator: ['overview', 'products', 'categories', 'navbar'],
      controller: ['overview', 'footer', 'about-us', 'contact-us'],
      admin: ['overview', 'products', 'categories', 'navbar', 'orders', 'footer', 'about-us', 'contact-us', 'slider', 'users']
    };

    const userPermissions = rolePermissions[user?.role] || [];
    return userPermissions.includes(permission);
  };

  // The value provided to consuming components
  const value = {
    user,
    token,
    isLoggedIn: !!token, // Convenience boolean
    loading,
    login,
    logout,
    // Role utilities
    hasRole,
    hasAnyRole,
    canAccessDashboard,
    hasPermission,
  };

  return (
    <AuthContext.Provider value={value}>
      {!loading && children}
    </AuthContext.Provider>
  );
};