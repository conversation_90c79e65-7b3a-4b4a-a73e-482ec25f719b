import React, { useState } from 'react';
import { NavLink, Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useCart } from '../../context/CartContext';
import { useNavbar } from '../../context/NavbarContext';
import { ShoppingCart, User, Menu, X, ChevronDown } from 'lucide-react';

// Helper function to ensure proper path formatting
const formatPath = (path) => {
  if (!path || path === '#') return path;
  // Ensure the path starts with a slash
  return path.startsWith('/') ? path : `/${path}`;
};

// --- Reusable NavLink Component for Desktop View ---
const DesktopNavLink = ({ link }) => {
  const activeLinkStyle = { color: '#4f46e5' };

  if (link.isDropdown) {
    return (
      <div className="relative group">
        <span className="flex items-center gap-1.5 cursor-default text-gray-600 hover:text-indigo-600 transition-colors duration-200 font-medium text-sm py-3">
          {link.title}
          {link.children && link.children.length > 0 && (
            <ChevronDown size={16} className="transition-transform duration-200 ease-out group-hover:rotate-180" />
          )}
        </span>
        {link.children && link.children.length > 0 && (
          <div className="absolute left-0 top-full pt-2.5 w-52 bg-white rounded-lg shadow-xl border border-slate-100/80 opacity-0 pointer-events-none transform scale-95 -translate-y-2 group-hover:opacity-100 group-hover:pointer-events-auto group-hover:scale-100 group-hover:translate-y-0 transition-all ease-out duration-200 origin-top">
            <div className="py-1">
              {link.children.map(child => (
                <Link
                  key={child._id}
                  to={formatPath(child.path)}
                  className="block w-full text-left px-4 py-2.5 text-sm font-medium text-slate-700 hover:bg-indigo-50 hover:text-indigo-600 transition-colors duration-150"
                >
                  {child.title}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <NavLink
      to={formatPath(link.path)}
      end
      style={({ isActive }) => (isActive ? activeLinkStyle : undefined)}
      className="flex items-center gap-2 text-gray-600 hover:text-indigo-600 transition-colors duration-200 font-medium text-sm py-3"
    >
      <span>{link.title}</span>
    </NavLink>
  );
};

// --- Reusable NavLink Component for Mobile View ---
const MobileNavLink = ({ link, onLinkClick }) => {
  const [isSubMenuOpen, setIsSubMenuOpen] = useState(false);
  const activeLinkStyle = { color: '#4f46e5', backgroundColor: '#f0f2ff' };

  if (link.isDropdown) {
    return (
      <div>
        <button
          onClick={() => setIsSubMenuOpen(!isSubMenuOpen)}
          className="w-full flex justify-between items-center gap-3 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50"
        >
          <span>{link.title}</span>
          {link.children && link.children.length > 0 && (
            <ChevronDown size={20} className={`transition-transform duration-300 ease-in-out ${isSubMenuOpen ? 'rotate-180' : ''}`} />
          )}
        </button>
        <div className={`grid transition-all duration-300 ease-in-out ${isSubMenuOpen ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}>
          <div className="overflow-hidden">
            <div className="pl-6 pt-1 pb-1 space-y-1">
              {link.children.map(child => (
                <NavLink
                  key={child._id}
                  to={formatPath(child.path)}
                  end
                  style={({ isActive }) => (isActive ? activeLinkStyle : undefined)}
                  className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50"
                  onClick={onLinkClick}
                >
                  {child.title}
                </NavLink>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }
    
  return (
    <NavLink
      to={formatPath(link.path)}
      end
      style={({ isActive }) => (isActive ? activeLinkStyle : undefined)}
      className="block px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50"
      onClick={onLinkClick}
    >
      {link.title}
    </NavLink>
  );
};


// --- Main Navbar Component ---
const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isLoggedIn, logout, canAccessDashboard, hasRole } = useAuth();
  const { cartCount, clearCart } = useCart();
  const { navLinks, loading: navLoading } = useNavbar();

  const handleLogoutClick = () => {
    clearCart(); // Clear the cart
    logout(); // Perform logout
    setIsMobileMenuOpen(false); // Close mobile menu if open
  };

  // --- Define static links that are always present ---
  const staticLinks = [
    { _id: 'static-home', title: 'Home', path: '/', isDropdown: false, children: [] },
    { _id: 'static-products', title: 'Store', path: '/products', isDropdown: false, children: [] },
    { _id: 'static-about', title: 'About Us', path: '/about-us', isDropdown: false, children: [] },
    { _id: 'static-contact', title: 'Contact Us', path: '/contact-us', isDropdown: false, children: [] },
    { _id: 'static-contact', title: 'Categories', path: '/categories', isDropdown: false, children: [] },
  ];

  // --- Filter out any dynamic links that conflict with static link paths to avoid duplication ---
  const staticPaths = staticLinks.map(l => l.path);
  const filteredDynamicLinks = navLoading 
    ? [] 
    : navLinks.filter(dynamicLink => !staticPaths.includes(dynamicLink.path));

  return (
    <header className="bg-white/80 backdrop-blur-md shadow-sm fixed top-0 left-0 w-full z-50">
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="text-2xl font-bold text-indigo-600">
            Shopify
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex md:items-center md:space-x-6">
            {/* Render Static Links */}
            {staticLinks.map(link => <DesktopNavLink key={link._id} link={link} />)}

            {/* Render Filtered Dynamic Links */}
            {navLoading 
              ? <span className="text-sm text-gray-500">Loading...</span> 
              : filteredDynamicLinks.map(link => <DesktopNavLink key={link._id} link={link} />)
            }
          </div>

          <div className="flex items-center space-x-4">
            <Link to="/cart" aria-label="Shopping Cart" className="relative text-gray-600 hover:text-indigo-600">
              <ShoppingCart size={24} />
              {cartCount > 0 && (
                <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  {cartCount}
                </span>
              )}
            </Link>
            
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="md:hidden text-gray-600 hover:text-indigo-600"
              aria-label="Toggle menu"
            >
              {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>

            {/* User menu for desktop */}
            <div className="hidden md:flex items-center space-x-4">
              {!isLoggedIn ? (
                <Link to="/login" className="text-gray-600 hover:text-indigo-600 font-medium text-sm">
                  Login
                </Link>
              ) : (
                <>
                  {canAccessDashboard() && (
                    <Link
                      to={hasRole('admin') ? "/admin" : "/dashboard"}
                      className="text-gray-600 hover:text-indigo-600"
                      title="Dashboard"
                    >
                      <User size={24} />
                    </Link>
                  )}
                  <button onClick={handleLogoutClick} className="text-gray-600 hover:text-indigo-600 font-medium text-sm">
                    Logout
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile menu */}
      <div className={`md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`}>
        <div className="px-2 pt-2 pb-3 space-y-1">
          {/* Render Static Links */}
          {staticLinks.map(link => (
            <MobileNavLink key={link._id} link={link} onLinkClick={() => setIsMobileMenuOpen(false)} />
          ))}

          {/* Render Separator if dynamic links exist */}
          {filteredDynamicLinks.length > 0 && <div className="border-t my-2" />}

          {/* Render Filtered Dynamic Links */}
          {filteredDynamicLinks.map(link => (
            <MobileNavLink key={link._id} link={link} onLinkClick={() => setIsMobileMenuOpen(false)} />
          ))}

          <div className="border-t my-2" />
          {!isLoggedIn ? (
            <Link 
              to="/login" 
              className="flex items-center gap-3 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50" 
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Login
            </Link>
          ) : (
            <>
              {canAccessDashboard() && (
                <Link
                  to={hasRole('admin') ? "/admin" : "/dashboard"}
                  className="flex items-center gap-3 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <User size={20} />
                  Dashboard
                </Link>
              )}
              <button 
                onClick={handleLogoutClick}
                className="w-full text-left px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-indigo-600 hover:bg-gray-50"
              >
                Logout
              </button>
            </>
          )}
        </div>
      </div>
    </header>
  );
};

export default Navbar;