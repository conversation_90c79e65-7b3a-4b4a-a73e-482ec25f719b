import React, { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { Grid, List, Search, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import RemoveModal from '../../../hooks/RemoveModal';
import CategoryForm from './components/CategoryForm';
import CategoryGrid from './components/CategoryGrid';

const CategoryManagementPage = () => {
    const [categories, setCategories] = useState([]);
    const [navLinks, setNavLinks] = useState([]);
    const [newCategoryName, setNewCategoryName] = useState('');
    const [selectedNavLink, setSelectedNavLink] = useState('');
    const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
    const [categoryToDelete, setCategoryToDelete] = useState(null);
    const [viewMode, setViewMode] = useState('grid');
    const [searchQuery, setSearchQuery] = useState('');
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [formLoading, setFormLoading] = useState(false);
    const [imageUrl, setImageUrl] = useState('');

    const API_BASE_URL = 'http://localhost:5000/api';

    const fetchInitialData = useCallback(async () => {
        setLoading(true);
        try {
            const [catRes, navLinkRes] = await Promise.all([
                axios.get(`${API_BASE_URL}/categories`),
                axios.get(`${API_BASE_URL}/navlinks?flat=true`)
            ]);
            setCategories(catRes.data);
            setNavLinks(navLinkRes.data.filter(link => !link.isDropdown));
        } catch (err) {
            setError('Failed to fetch page data.');
            console.error('Error fetching data:', err);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchInitialData();
    }, [fetchInitialData]);

    const handleCreate = async (e) => {
        e.preventDefault();
        if (!newCategoryName.trim()) {
            setError('Category name is required');
            return;
        }
        setFormLoading(true);
        setError('');

        try {
            const categoryData = {
                name: newCategoryName.trim(),
                imageUrl: imageUrl.trim() || undefined,
                navLink: selectedNavLink || undefined
            };

            const response = await axios.post(`${API_BASE_URL}/categories`, categoryData);
            setCategories(prev => [...prev, response.data]);
            setNewCategoryName('');
            setImageUrl('');
            setSelectedNavLink('');
        } catch (err) {
            setError(err.response?.data?.message || 'Failed to create category');
        } finally {
            setFormLoading(false);
        }
    };

    const handleDelete = (categoryId) => {
        setCategoryToDelete(categoryId);
        setIsRemoveModalOpen(true);
    };

    const confirmDelete = async () => {
        if (!categoryToDelete) return;

        try {
            await axios.delete(`${API_BASE_URL}/categories/${categoryToDelete}`);
            setCategories(prev => prev.filter(cat => cat._id !== categoryToDelete));
            setIsRemoveModalOpen(false);
            setCategoryToDelete(null);
        } catch (err) {
            setError('Failed to delete category');
        }
    };

    const filteredCategories = categories.filter(category =>
        category.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    return (
        <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
                    <h1 className="text-3xl font-bold text-gray-800">Category Management</h1>
                    <div className="mt-4 sm:mt-0 flex items-center space-x-4">
                        <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                            <input
                                type="text"
                                placeholder="Search categories..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                            />
                        </div>
                        <div className="flex items-center space-x-2 bg-white rounded-lg border border-gray-200 p-1">
                            <button
                                onClick={() => setViewMode('grid')}
                                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-indigo-50 text-indigo-600' : 'text-gray-400'}`}
                            >
                                <Grid size={18} />
                            </button>
                            <button
                                onClick={() => setViewMode('list')}
                                className={`p-2 rounded ${viewMode === 'list' ? 'bg-indigo-50 text-indigo-600' : 'text-gray-400'}`}
                            >
                                <List size={18} />
                            </button>
                        </div>
                    </div>
                </div>

                <AnimatePresence>
                    {error && (
                        <motion.div
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6 rounded-lg flex items-center justify-between"
                        >
                            <span>{error}</span>
                            <button onClick={() => setError('')} className="text-red-500 hover:text-red-700">
                                <X size={18} />
                            </button>
                        </motion.div>
                    )}
                </AnimatePresence>

                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <div className="lg:col-span-1">
                        <CategoryForm
                            newCategoryName={newCategoryName}
                            setNewCategoryName={setNewCategoryName}
                            imageUrl={imageUrl}
                            setImageUrl={setImageUrl}
                            selectedNavLink={selectedNavLink}
                            setSelectedNavLink={setSelectedNavLink}
                            navLinks={navLinks}
                            onSubmit={handleCreate}
                            formLoading={formLoading}
                        />
                    </div>

                    <div className="lg:col-span-3">
                        <CategoryGrid
                            loading={loading}
                            filteredCategories={filteredCategories}
                            searchQuery={searchQuery}
                            viewMode={viewMode}
                            onDelete={handleDelete}
                        />
                    </div>
                </div>
            </div>

            <RemoveModal
                isOpen={isRemoveModalOpen}
                onClose={() => setIsRemoveModalOpen(false)}
                onConfirm={confirmDelete}
                title="Delete Category"
                message="Are you sure you want to delete this category? This action cannot be undone."
            />
        </div>
    );
};

export default CategoryManagementPage;
