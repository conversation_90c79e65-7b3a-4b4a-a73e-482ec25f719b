import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Route, Routes, Link } from 'react-router-dom';

// --- PROVIDERS ---
import { AuthProvider } from './context/AuthContext';
import { CartProvider } from './context/CartContext';
import { NavbarProvider, useNavbar } from './context/NavbarContext';

// --- LAYOUTS ---
import DashboardLayout from './pages/Dashboard/components/Sidebar';
import MainLayout from './layouts/MainLayout';

// --- PAGE COMPONENTS ---
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import VerificationPage from './pages/auth/VerificationPage';

import HomePage from './pages/Home/HomePage';
import ProductDisplayPage from './pages/Product/components/product_page/ProductDisplayPage';
import ProductDetailPage from './pages/Product/ProductDetailPage';
import CartPage from './pages/cart/CartPage';

import DashboardHomePage from './pages/Dashboard/dashboardhome/DashboardHomePage';
import ProductManagementPage from './pages/Dashboard/createproduct/CreateProductPage';
import SliderManagementPage from './pages/Dashboard/slidermanagement/SliderManagementPage';
import NavbarManagementPage from './pages/Dashboard/navbarmanagement/NavbarManagementPage';
import OrdersManagementPage from './pages/Dashboard/ordersmanagement/OrdersManagementPage';
import CategoryManagementPage from './pages/Dashboard/categorymanagement/CategoryManagementPage';
import FooterManagementPage from './pages/Dashboard/footermanagement/FooterManagementPage';
import AboutUsManagementPage from './pages/Dashboard/aboutusmanagement/AboutUsManagementPage';
import ContactUsManagementPage from './pages/Dashboard/contactusmanagement/ContactUsManagementPage';
import UserManagementPage from './pages/Dashboard/usermanagement/UserManagementPage';

import { DashboardProtectedRoute, AdminProtectedRoute } from './common/protectRouter/ProtectedRoute';
import PermissionWrapper from './common/PermissionWrapper/PermissionWrapper';
import AboutUsPage from './pages/about/AboutUsPage';
import ContactUs from './pages/contactus/ContactUs';
import NotFound from './pages/404page/NotFound';

// --- ADDED PLACEHOLDER PAGES ---
// For simplicity, these are defined here. You can move them to their own files.
import ScrollToTop from './hooks/ScrollToTop';
import CategoriesPage from './pages/categories/CategoriesPage';





// This helper function remains the same
const flattenLinksToPaths = (links) => {
  let paths = [];
  if (!links) return [];
  links.forEach(link => {
    if (!link.isDropdown && link.path && link.path !== '#') {
      paths.push(link.path);
    }
    if (link.children && link.children.length > 0) {
      paths = paths.concat(flattenLinksToPaths(link.children));
    }
  });
  return paths;
};


const AppContent = () => {
  const { navLinks } = useNavbar();
  const [dynamicRoutes, setDynamicRoutes] = useState([]);

  useEffect(() => {
    if (navLinks && navLinks.length > 0) {
      const allPaths = flattenLinksToPaths(navLinks);
      setDynamicRoutes(allPaths);
    }
  }, [navLinks]);

  return (
    <Routes>
      {/* Auth Routes */}
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />
      <Route path="/verify-account" element={<VerificationPage />} />

      {/* Dashboard Routes */}
      <Route element={<DashboardProtectedRoute />}>
        <Route element={<DashboardLayout />}>
          <Route path="/dashboard" element={
            <PermissionWrapper permission="overview">
              <DashboardHomePage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/products" element={
            <PermissionWrapper permission="products">
              <ProductManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/slider" element={
            <PermissionWrapper permission="slider">
              <SliderManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/navbar" element={
            <PermissionWrapper permission="navbar">
              <NavbarManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/orders" element={
            <PermissionWrapper permission="orders">
              <OrdersManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/categories" element={
            <PermissionWrapper permission="categories">
              <CategoryManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/footer" element={
            <PermissionWrapper permission="footer">
              <FooterManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/about-us" element={
            <PermissionWrapper permission="about-us">
              <AboutUsManagementPage />
            </PermissionWrapper>
          } />
          <Route path="/dashboard/contact-us" element={
            <PermissionWrapper permission="contact-us">
              <ContactUsManagementPage />
            </PermissionWrapper>
          } />
        </Route>
      </Route>

      {/* Admin Routes */}
      <Route element={<AdminProtectedRoute />}>
        <Route element={<DashboardLayout />}>
          <Route path="/admin" element={<UserManagementPage />} />
        </Route>
      </Route>

      {/* Public-Facing Routes */}
      <Route element={<MainLayout />}>
        <Route path="/" element={<HomePage />} />
        <Route path="/products" element={<ProductDisplayPage />} />
        <Route path="/product/:id" element={<ProductDetailPage />} />
        <Route path="/cart" element={<CartPage />} />
        
        {/* --- ADDED STATIC ROUTES --- */}
        <Route path="/about-us" element={<AboutUsPage />} />
        <Route path="/contact-us" element={<ContactUs />} />
        <Route path="/categories" element={<CategoriesPage />} />

        {/* This creates product listing pages for paths defined in the navbar */}
        {dynamicRoutes.map((path) => (
          <Route
            key={path}
            path={path}
            element={<ProductDisplayPage />}
          />
        ))}

        {/* 404 Route - Must be last */}
        <Route path="*" element={<NotFound />} />
      </Route>
    </Routes>
  );
}

// The main App component wraps everything in providers
function App() {
  return (
    <Router>
      <ScrollToTop />
      <AuthProvider>
        <CartProvider>
          <NavbarProvider>
            <AppContent />
          </NavbarProvider>
        </CartProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;