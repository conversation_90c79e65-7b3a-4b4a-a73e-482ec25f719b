import React, { useState, useEffect } from 'react';
import { Save } from 'lucide-react';
import BrandInfoTab from './components/BrandInfoTab';
import SocialMediaTab from './components/SocialMediaTab';
import FooterSectionsTab from './components/FooterSectionsTab';
import WhyShopWithUsTab from './components/WhyShopWithUsTab';
import LegalLinksTab from './components/LegalLinksTab';
import NewsletterTab from './components/NewsletterTab';

const FooterManagementPage = () => {
  const [footerData, setFooterData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('brand');
  const API_URL = 'http://localhost:5000/api/footer';

  useEffect(() => {
    fetchFooterData();
  }, []);

  const fetchFooterData = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetch(`${API_URL}/admin`);
      if (!response.ok) {
        if (response.status === 404) {
          // No footer config exists, create default
          setFooterData({
            brandInfo: { name: 'StyleHub', description: '', logo: '' },
            socialMedia: [],
            sections: [],
            legalLinks: [],
            whyShopWithUs: { title: 'Why Shop With Us?', features: [], isActive: true },
            newsletter: { title: 'Stay Updated', description: '', isActive: true },
            copyright: { text: 'StyleHub. All Rights Reserved.', year: new Date().getFullYear() }
          });
        } else {
          throw new Error('Failed to fetch footer data.');
        }
      } else {
        const data = await response.json();
        setFooterData(data);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const saveFooterData = async () => {
    try {
      setSaving(true);
      const response = await fetch(API_URL, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(footerData)
      });
      if (!response.ok) throw new Error('Failed to save footer data.');
      const updatedData = await response.json();
      setFooterData(updatedData);
      alert('Footer data saved successfully!');
    } catch (err) {
      alert('Error saving footer data: ' + err.message);
    } finally {
      setSaving(false);
    }
  };

  // Brand Info handlers
  const updateBrandInfo = (field, value) => {
    setFooterData(prev => ({
      ...prev,
      brandInfo: { ...prev.brandInfo, [field]: value }
    }));
  };

  // Social Media handlers
  const addSocialMedia = () => {
    setFooterData(prev => ({
      ...prev,
      socialMedia: [...prev.socialMedia, { platform: 'facebook', url: '', isActive: true }]
    }));
  };

  const updateSocialMedia = (index, field, value) => {
    setFooterData(prev => ({
      ...prev,
      socialMedia: prev.socialMedia.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeSocialMedia = (index) => {
    setFooterData(prev => ({
      ...prev,
      socialMedia: prev.socialMedia.filter((_, i) => i !== index)
    }));
  };

  // Footer Sections handlers
  const addSection = () => {
    setFooterData(prev => ({
      ...prev,
      sections: [...prev.sections, { title: '', links: [], order: prev.sections.length, isActive: true }]
    }));
  };

  const updateSection = (index, field, value) => {
    setFooterData(prev => ({
      ...prev,
      sections: prev.sections.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeSection = (index) => {
    setFooterData(prev => ({
      ...prev,
      sections: prev.sections.filter((_, i) => i !== index)
    }));
  };

  const addLinkToSection = (sectionIndex) => {
    setFooterData(prev => ({
      ...prev,
      sections: prev.sections.map((section, i) => 
        i === sectionIndex 
          ? { ...section, links: [...section.links, { text: '', url: '', isActive: true }] }
          : section
      )
    }));
  };

  const updateSectionLink = (sectionIndex, linkIndex, field, value) => {
    setFooterData(prev => ({
      ...prev,
      sections: prev.sections.map((section, i) => 
        i === sectionIndex 
          ? {
              ...section,
              links: section.links.map((link, j) => 
                j === linkIndex ? { ...link, [field]: value } : link
              )
            }
          : section
      )
    }));
  };

  const removeSectionLink = (sectionIndex, linkIndex) => {
    setFooterData(prev => ({
      ...prev,
      sections: prev.sections.map((section, i) =>
        i === sectionIndex
          ? { ...section, links: section.links.filter((_, j) => j !== linkIndex) }
          : section
      )
    }));
  };

  // Legal Links handlers
  const addLegalLink = () => {
    setFooterData(prev => ({
      ...prev,
      legalLinks: [...prev.legalLinks, { text: '', url: '', isActive: true }]
    }));
  };

  const updateLegalLink = (index, field, value) => {
    setFooterData(prev => ({
      ...prev,
      legalLinks: prev.legalLinks.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    }));
  };

  const removeLegalLink = (index) => {
    setFooterData(prev => ({
      ...prev,
      legalLinks: prev.legalLinks.filter((_, i) => i !== index)
    }));
  };

  // Why Shop With Us handlers
  const addFeature = () => {
    setFooterData(prev => ({
      ...prev,
      whyShopWithUs: {
        ...prev.whyShopWithUs,
        features: [...prev.whyShopWithUs.features, { title: '', description: '', icon: '', isActive: true }]
      }
    }));
  };

  const updateFeature = (index, field, value) => {
    setFooterData(prev => ({
      ...prev,
      whyShopWithUs: {
        ...prev.whyShopWithUs,
        features: prev.whyShopWithUs.features.map((item, i) =>
          i === index ? { ...item, [field]: value } : item
        )
      }
    }));
  };

  const removeFeature = (index) => {
    setFooterData(prev => ({
      ...prev,
      whyShopWithUs: {
        ...prev.whyShopWithUs,
        features: prev.whyShopWithUs.features.filter((_, i) => i !== index)
      }
    }));
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error: {error}</p>
          <button
            onClick={fetchFooterData}
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold text-gray-800">Footer Management</h1>
        <button
          onClick={saveFooterData}
          disabled={saving}
          className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <Save size={20} />
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex space-x-8">
          {[
            { id: 'brand', label: 'Brand Info' },
            { id: 'social', label: 'Social Media' },
            { id: 'sections', label: 'Footer Sections' },
            { id: 'features', label: 'Why Shop With Us' },
            { id: 'legal', label: 'Legal Links' },
            { id: 'newsletter', label: 'Newsletter' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {activeTab === 'brand' && (
          <BrandInfoTab
            footerData={footerData}
            updateBrandInfo={updateBrandInfo}
            setFooterData={setFooterData}
          />
        )}

        {activeTab === 'social' && (
          <SocialMediaTab
            footerData={footerData}
            addSocialMedia={addSocialMedia}
            updateSocialMedia={updateSocialMedia}
            removeSocialMedia={removeSocialMedia}
          />
        )}

        {activeTab === 'sections' && (
          <FooterSectionsTab
            footerData={footerData}
            addSection={addSection}
            updateSection={updateSection}
            removeSection={removeSection}
            addLinkToSection={addLinkToSection}
            updateSectionLink={updateSectionLink}
            removeSectionLink={removeSectionLink}
          />
        )}

        {activeTab === 'features' && (
          <WhyShopWithUsTab
            footerData={footerData}
            setFooterData={setFooterData}
            addFeature={addFeature}
            updateFeature={updateFeature}
            removeFeature={removeFeature}
          />
        )}

        {activeTab === 'legal' && (
          <LegalLinksTab
            footerData={footerData}
            addLegalLink={addLegalLink}
            updateLegalLink={updateLegalLink}
            removeLegalLink={removeLegalLink}
          />
        )}

        {activeTab === 'newsletter' && (
          <NewsletterTab
            footerData={footerData}
            setFooterData={setFooterData}
          />
        )}
      </div>
    </div>
  );
};

export default FooterManagementPage;
