import React, { useState, useEffect } from 'react';
import { Save, AlertCircle, CheckCircle } from 'lucide-react';
import HeroSectionTab from './components/HeroSectionTab';
import CoreValuesTab from './components/CoreValuesTab';
import TimelineTab from './components/TimelineTab';
import OurMembersTab from './components/OurMembersTab';

const AboutUsManagementPage = () => {
  const [activeTab, setActiveTab] = useState('hero');
  const [aboutUsData, setAboutUsData] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  const API_URL = 'http://localhost:5000/api/about-us';

  useEffect(() => {
    fetchAboutUsData();
  }, []);

  const fetchAboutUsData = async () => {
    try {
      setLoading(true);
      setError('');
      const response = await fetch(`${API_URL}/admin`);
      
      if (!response.ok) {
        if (response.status === 404) {
          // No about us data exists, create default structure
          setAboutUsData(getDefaultData());
        } else {
          throw new Error('Failed to fetch about us data.');
        }
      } else {
        const data = await response.json();
        setAboutUsData(data);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const getDefaultData = () => ({
    heroSection: {
      title: { fr: '', ar: '' },
      highlightedText: { fr: '', ar: '' },
      subtitle: { fr: '', ar: '' },
      buttonText: { fr: '', ar: '' },
      backgroundImage: ''
    },
    coreValues: {
      vision: {
        title: { fr: '', ar: '' },
        description: { fr: '', ar: '' },
        points: { fr: [], ar: [] },
        image: ''
      },
      mission: {
        title: { fr: '', ar: '' },
        description: { fr: '', ar: '' },
        points: { fr: [], ar: [] },
        image: ''
      },
      story: {
        title: { fr: '', ar: '' },
        description: { fr: '', ar: '' },
        points: { fr: [], ar: [] },
        image: ''
      }
    },
    timeline: [],
    ourMembers: {
      title: { fr: '', ar: '' },
      subtitle: { fr: '', ar: '' },
      members: []
    }
  });

  const saveAboutUsData = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccessMessage('');

      const response = await fetch(API_URL, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(aboutUsData)
      });

      if (!response.ok) {
        throw new Error('Failed to save about us data.');
      }

      const updatedData = await response.json();
      setAboutUsData(updatedData);
      setSuccessMessage('About Us content saved successfully!');
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (err) {
      setError('Error saving about us data: ' + err.message);
    } finally {
      setSaving(false);
    }
  };

  const handleDataChange = (section, newData) => {
    setAboutUsData(prev => ({
      ...prev,
      [section]: newData
    }));
  };

  const tabs = [
    { id: 'hero', label: 'Hero Section', icon: '🎯' },
    { id: 'values', label: 'Core Values', icon: '💎' },
    { id: 'timeline', label: 'Timeline', icon: '📅' },
    { id: 'members', label: 'Our Members', icon: '👥' }
  ];

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">About Us Management</h1>
          <p className="text-gray-600">Manage all content for the About Us page. Language switching is controlled from the footer.</p>
        </div>

        <div className="mt-4 lg:mt-0">
          {/* Save Button */}
          <button
            onClick={saveAboutUsData}
            disabled={saving}
            className="flex items-center gap-2 px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <Save size={20} />
            {saving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>

      {/* Messages */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3">
          <AlertCircle size={20} className="text-red-600 flex-shrink-0" />
          <p className="text-red-800">{error}</p>
        </div>
      )}

      {successMessage && (
        <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3">
          <CheckCircle size={20} className="text-green-600 flex-shrink-0" />
          <p className="text-green-800">{successMessage}</p>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex space-x-8 overflow-x-auto">
          {tabs.map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-4 px-2 border-b-2 font-medium text-sm whitespace-nowrap transition-colors ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-gray-50 rounded-xl p-6">
        {activeTab === 'hero' && (
          <HeroSectionTab
            data={aboutUsData}
            onChange={handleDataChange}
          />
        )}

        {activeTab === 'values' && (
          <CoreValuesTab
            data={aboutUsData}
            onChange={handleDataChange}
          />
        )}

        {activeTab === 'timeline' && (
          <TimelineTab
            data={aboutUsData}
            onChange={handleDataChange}
          />
        )}

        {activeTab === 'members' && (
          <OurMembersTab
            data={aboutUsData}
            onChange={handleDataChange}
          />
        )}
      </div>
    </div>
  );
};

export default AboutUsManagementPage;
